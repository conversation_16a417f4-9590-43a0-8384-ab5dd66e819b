package com.kang.contractdemo.service;

import com.kang.contractdemo.config.ContractConfig;
import com.kang.contractdemo.util.WordUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * Word文档处理服务
 */
@Service
public class WordService {

    private static final Logger logger = LoggerFactory.getLogger(WordService.class);

    @Autowired
    private ContractConfig contractConfig;

    /**
     * 应用启动后自动读取Word文档
     */
    @PostConstruct
    public void readWordDocumentOnStartup() {
        try {
            String wordPath = contractConfig.getWordPath();
            
            if (wordPath == null || wordPath.trim().isEmpty()) {
                logger.info("未配置Word文档路径，跳过Word文档读取");
                return;
            }

            logger.info("=== Word文档读取开始 ===");
            logger.info("Word文档路径: {}", wordPath);

            // 读取Word文档并输出前1000行到控制台
            readWordDocument();

            logger.info("=== Word文档读取完成 ===");

        } catch (Exception e) {
            logger.error("Word文档读取失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 读取Word文档内容
     */
    public void readWordDocument() {
        String wordPath = contractConfig.getWordPath();
        if (wordPath == null || wordPath.trim().isEmpty()) {
            throw new RuntimeException("Word文档路径未配置");
        }
        
        WordUtil.readWordAndPrintToConsole(wordPath, 1000);
    }

    /**
     * 读取Word文档内容（指定行数）
     *
     * @param maxLines 最大读取行数
     */
    public void readWordDocument(int maxLines) {
        String wordPath = contractConfig.getWordPath();
        if (wordPath == null || wordPath.trim().isEmpty()) {
            throw new RuntimeException("Word文档路径未配置");
        }
        
        WordUtil.readWordAndPrintToConsole(wordPath, maxLines);
    }

    /**
     * 获取Word文档路径
     *
     * @return Word文档路径
     */
    public String getWordPath() {
        return contractConfig.getWordPath();
    }

    /**
     * 获取Word文档信息
     *
     * @return Word文档信息
     */
    public WordUtil.WordDocumentInfo getWordDocumentInfo() {
        String wordPath = contractConfig.getWordPath();
        if (wordPath == null || wordPath.trim().isEmpty()) {
            throw new RuntimeException("Word文档路径未配置");
        }
        
        return WordUtil.getDocumentInfo(wordPath);
    }

    /**
     * 提取Word文档文本内容
     *
     * @return Word文档文本内容
     */
    public String extractWordText() {
        try {
            String wordPath = contractConfig.getWordPath();
            if (wordPath == null || wordPath.trim().isEmpty()) {
                throw new RuntimeException("Word文档路径未配置");
            }
            
            logger.info("开始提取Word文档文本内容: {}", wordPath);
            
            String text = WordUtil.extractTextFromWord(wordPath);
            
            logger.info("Word文档文本提取完成，文本长度: {} 字符", text.length());
            
            return text;
            
        } catch (Exception e) {
            logger.error("提取Word文档文本失败: {}", e.getMessage(), e);
            throw new RuntimeException("提取Word文档文本失败: " + e.getMessage(), e);
        }
    }
}
