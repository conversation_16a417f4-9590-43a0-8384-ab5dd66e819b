package com.kang.contractdemo.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 合同配置类
 * 用于读取application.yml中的contract配置
 */
@Component
@ConfigurationProperties(prefix = "contract")
public class ContractConfig {

    /**
     * PDF模板文件路径
     */
    private String path;

    /**
     * Word文档文件路径
     */
    private String wordPath;

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getWordPath() {
        return wordPath;
    }

    public void setWordPath(String wordPath) {
        this.wordPath = wordPath;
    }
}