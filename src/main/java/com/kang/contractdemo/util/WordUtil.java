package com.kang.contractdemo.util;

import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;

/**
 * Word文档工具类
 * 支持读取.doc和.docx格式的Word文档
 */
public class WordUtil {

    private static final Logger logger = LoggerFactory.getLogger(WordUtil.class);

    /**
     * 读取Word文档内容并输出前1000行到控制台
     *
     * @param filePath Word文档文件路径
     */
    public static void readWordAndPrintToConsole(String filePath) {
        readWordAndPrintToConsole(filePath, 1000);
    }

    /**
     * 读取Word文档内容并输出指定行数到控制台
     *
     * @param filePath Word文档文件路径
     * @param maxLines 最大输出行数
     */
    public static void readWordAndPrintToConsole(String filePath, int maxLines) {
        try {
            logger.info("开始读取Word文档: {}", filePath);
            
            String content = extractTextFromWord(filePath);
            
            if (content == null || content.trim().isEmpty()) {
                System.out.println("Word文档内容为空或无法读取");
                return;
            }

            // 按行分割内容
            String[] lines = content.split("\\r?\\n");
            
            // 输出格式化的标题
            printHeader(filePath, lines.length, maxLines);
            
            // 输出内容
            int outputLines = Math.min(lines.length, maxLines);
            for (int i = 0; i < outputLines; i++) {
                String line = lines[i].trim();
                if (!line.isEmpty()) {
                    System.out.printf("%4d: %s%n", i + 1, line);
                } else {
                    System.out.printf("%4d: %n", i + 1);
                }
            }
            
            // 输出结尾信息
            printFooter(lines.length, outputLines);
            
            logger.info("Word文档读取完成，总行数: {}, 输出行数: {}", lines.length, outputLines);
            
        } catch (Exception e) {
            logger.error("读取Word文档失败: {}", e.getMessage(), e);
            System.err.println("读取Word文档失败: " + e.getMessage());
        }
    }

    /**
     * 从Word文档中提取文本内容
     *
     * @param filePath Word文档文件路径
     * @return 文档文本内容
     * @throws IOException 文件读取异常
     */
    public static String extractTextFromWord(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new IOException("Word文档不存在: " + filePath);
        }

        String fileName = file.getName().toLowerCase();
        
        if (fileName.endsWith(".docx")) {
            return extractTextFromDocx(filePath);
        } else if (fileName.endsWith(".doc")) {
            return extractTextFromDoc(filePath);
        } else {
            throw new IOException("不支持的文件格式，仅支持.doc和.docx格式: " + filePath);
        }
    }

    /**
     * 从.docx文档中提取文本
     *
     * @param filePath .docx文档路径
     * @return 文档文本内容
     * @throws IOException 文件读取异常
     */
    private static String extractTextFromDocx(String filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        
        try (FileInputStream fis = new FileInputStream(filePath);
             XWPFDocument document = new XWPFDocument(fis)) {
            
            List<XWPFParagraph> paragraphs = document.getParagraphs();
            for (XWPFParagraph paragraph : paragraphs) {
                String text = paragraph.getText();
                if (text != null && !text.trim().isEmpty()) {
                    content.append(text).append("\n");
                }
            }
        }
        
        return content.toString();
    }

    /**
     * 从.doc文档中提取文本
     *
     * @param filePath .doc文档路径
     * @return 文档文本内容
     * @throws IOException 文件读取异常
     */
    private static String extractTextFromDoc(String filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             HWPFDocument document = new HWPFDocument(fis);
             WordExtractor extractor = new WordExtractor(document)) {
            
            return extractor.getText();
        }
    }

    /**
     * 打印标题信息
     *
     * @param filePath 文件路径
     * @param totalLines 总行数
     * @param maxLines 最大显示行数
     */
    private static void printHeader(String filePath, int totalLines, int maxLines) {
        String separator = repeatString("=", 80);
        System.out.println("\n" + separator);
        System.out.println("\t\t\tWord文档内容读取");
        System.out.println(separator);
        System.out.println("文件路径: " + filePath);
        System.out.println("文档总行数: " + totalLines);
        System.out.println("显示行数: " + Math.min(totalLines, maxLines));
        System.out.println(separator);
    }

    /**
     * 打印结尾信息
     *
     * @param totalLines 总行数
     * @param outputLines 输出行数
     */
    private static void printFooter(int totalLines, int outputLines) {
        String separator = repeatString("=", 80);
        System.out.println(separator);
        
        if (outputLines < totalLines) {
            System.out.println("注意: 文档还有 " + (totalLines - outputLines) + " 行未显示");
        }
        
        System.out.println("Word文档读取完成！");
        System.out.println(separator + "\n");
    }

    /**
     * 重复字符串（Java 8兼容版本）
     *
     * @param str 要重复的字符串
     * @param count 重复次数
     * @return 重复后的字符串
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 获取Word文档基本信息
     *
     * @param filePath Word文档文件路径
     * @return 文档信息
     */
    public static WordDocumentInfo getDocumentInfo(String filePath) {
        try {
            String content = extractTextFromWord(filePath);
            String[] lines = content.split("\\r?\\n");
            
            return new WordDocumentInfo(
                filePath,
                content.length(),
                lines.length,
                new File(filePath).length()
            );
            
        } catch (Exception e) {
            logger.error("获取Word文档信息失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Word文档信息类
     */
    public static class WordDocumentInfo {
        private final String filePath;
        private final int characterCount;
        private final int lineCount;
        private final long fileSize;

        public WordDocumentInfo(String filePath, int characterCount, int lineCount, long fileSize) {
            this.filePath = filePath;
            this.characterCount = characterCount;
            this.lineCount = lineCount;
            this.fileSize = fileSize;
        }

        public String getFilePath() {
            return filePath;
        }

        public int getCharacterCount() {
            return characterCount;
        }

        public int getLineCount() {
            return lineCount;
        }

        public long getFileSize() {
            return fileSize;
        }

        @Override
        public String toString() {
            return String.format("WordDocumentInfo{filePath='%s', characterCount=%d, lineCount=%d, fileSize=%d bytes}",
                    filePath, characterCount, lineCount, fileSize);
        }
    }
}
