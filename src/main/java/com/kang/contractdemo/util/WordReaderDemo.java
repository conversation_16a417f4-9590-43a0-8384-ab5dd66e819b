package com.kang.contractdemo.util;

/**
 * Word文档读取演示类
 * 演示如何使用WordUtil工具类读取Word文档
 */
public class WordReaderDemo {

    public static void main(String[] args) {
        // 示例用法
        String wordFilePath = "/Users/<USER>/Desktop/test-document.docx";
        
        System.out.println("=== Word文档读取工具演示 ===\n");
        
        // 方法1: 读取并输出前1000行（默认）
        System.out.println("1. 读取Word文档前1000行:");
        WordUtil.readWordAndPrintToConsole(wordFilePath);
        
        // 方法2: 读取并输出前50行
        System.out.println("\n2. 读取Word文档前50行:");
        WordUtil.readWordAndPrintToConsole(wordFilePath, 50);
        
        // 方法3: 获取文档信息
        System.out.println("\n3. 获取Word文档信息:");
        WordUtil.WordDocumentInfo info = WordUtil.getDocumentInfo(wordFilePath);
        if (info != null) {
            System.out.println(info);
        }
        
        // 方法4: 直接提取文本内容
        try {
            System.out.println("\n4. 直接提取文本内容（前200字符）:");
            String content = WordUtil.extractTextFromWord(wordFilePath);
            if (content != null && content.length() > 0) {
                String preview = content.length() > 200 ? 
                    content.substring(0, 200) + "..." : content;
                System.out.println("文本预览: " + preview);
            }
        } catch (Exception e) {
            System.err.println("提取文本失败: " + e.getMessage());
        }
    }
}
