package com.kang.contractdemo.controller;

import com.kang.contractdemo.service.WordService;
import com.kang.contractdemo.util.WordUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Word文档处理控制器
 */
@RestController
@RequestMapping("/api/word")
public class WordController {

    @Autowired
    private WordService wordService;

    /**
     * 读取Word文档内容到控制台
     *
     * @return 操作结果
     */
    @GetMapping("/read")
    public Map<String, Object> readWordDocument() {
        Map<String, Object> response = new HashMap<>();

        try {
            wordService.readWordDocument();

            response.put("success", true);
            response.put("message", "Word文档读取成功，内容已输出到控制台");
            response.put("data", null);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Word文档读取失败: " + e.getMessage());
            response.put("data", null);
        }

        return response;
    }

    /**
     * 读取Word文档内容到控制台（指定行数）
     *
     * @param maxLines 最大读取行数
     * @return 操作结果
     */
    @GetMapping("/read/{maxLines}")
    public Map<String, Object> readWordDocument(@PathVariable int maxLines) {
        Map<String, Object> response = new HashMap<>();

        try {
            if (maxLines <= 0) {
                throw new IllegalArgumentException("行数必须大于0");
            }

            wordService.readWordDocument(maxLines);

            response.put("success", true);
            response.put("message", "Word文档读取成功，前" + maxLines + "行内容已输出到控制台");
            Map<String, Object> data = new HashMap<>();
            data.put("maxLines", maxLines);
            response.put("data", data);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Word文档读取失败: " + e.getMessage());
            response.put("data", null);
        }

        return response;
    }

    /**
     * 获取Word文档路径
     *
     * @return Word文档路径信息
     */
    @GetMapping("/path")
    public Map<String, Object> getWordPath() {
        Map<String, Object> response = new HashMap<>();

        try {
            String wordPath = wordService.getWordPath();

            response.put("success", true);
            response.put("message", "获取Word文档路径成功");
            Map<String, Object> data = new HashMap<>();
            data.put("wordPath", wordPath);
            response.put("data", data);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取Word文档路径失败: " + e.getMessage());
            response.put("data", null);
        }

        return response;
    }

    /**
     * 获取Word文档信息
     *
     * @return Word文档信息
     */
    @GetMapping("/info")
    public Map<String, Object> getWordDocumentInfo() {
        Map<String, Object> response = new HashMap<>();

        try {
            WordUtil.WordDocumentInfo info = wordService.getWordDocumentInfo();

            response.put("success", true);
            response.put("message", "获取Word文档信息成功");
            Map<String, Object> data = new HashMap<>();
            data.put("filePath", info.getFilePath());
            data.put("characterCount", info.getCharacterCount());
            data.put("lineCount", info.getLineCount());
            data.put("fileSize", info.getFileSize());
            response.put("data", data);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取Word文档信息失败: " + e.getMessage());
            response.put("data", null);
        }

        return response;
    }

    /**
     * 提取Word文档文本内容
     *
     * @return Word文档文本内容
     */
    @GetMapping("/text")
    public Map<String, Object> extractWordText() {
        Map<String, Object> response = new HashMap<>();

        try {
            String text = wordService.extractWordText();

            response.put("success", true);
            response.put("message", "提取Word文档文本成功");
            Map<String, Object> data = new HashMap<>();
            data.put("text", text);
            data.put("textLength", text.length());
            response.put("data", data);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "提取Word文档文本失败: " + e.getMessage());
            response.put("data", null);
        }

        return response;
    }

    /**
     * 提取Word文档文本内容（预览版，只返回前500字符）
     *
     * @return Word文档文本预览
     */
    @GetMapping("/text/preview")
    public Map<String, Object> extractWordTextPreview() {
        Map<String, Object> response = new HashMap<>();

        try {
            String text = wordService.extractWordText();
            String preview = text.length() > 500 ? text.substring(0, 500) + "..." : text;

            response.put("success", true);
            response.put("message", "提取Word文档文本预览成功");
            Map<String, Object> data = new HashMap<>();
            data.put("preview", preview);
            data.put("fullTextLength", text.length());
            data.put("isPreview", text.length() > 500);
            response.put("data", data);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "提取Word文档文本预览失败: " + e.getMessage());
            response.put("data", null);
        }

        return response;
    }
}
