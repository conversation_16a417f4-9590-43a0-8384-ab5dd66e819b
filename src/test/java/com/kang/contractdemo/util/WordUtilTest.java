package com.kang.contractdemo.util;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Word工具类测试
 */
@SpringBootTest
public class WordUtilTest {

    @Test
    public void testWordDocumentInfoCreation() {
        // 测试WordDocumentInfo类的创建和getter方法
        String filePath = "/test/path/document.docx";
        int characterCount = 1000;
        int lineCount = 50;
        long fileSize = 25600;

        WordUtil.WordDocumentInfo info = new WordUtil.WordDocumentInfo(
                filePath, characterCount, lineCount, fileSize);

        assertEquals(filePath, info.getFilePath());
        assertEquals(characterCount, info.getCharacterCount());
        assertEquals(lineCount, info.getLineCount());
        assertEquals(fileSize, info.getFileSize());

        // 测试toString方法
        String expected = String.format(
                "WordDocumentInfo{filePath='%s', characterCount=%d, lineCount=%d, fileSize=%d bytes}",
                filePath, characterCount, lineCount, fileSize);
        assertEquals(expected, info.toString());
    }

    @Test
    public void testExtractTextFromWordWithNonExistentFile() {
        // 测试不存在的文件
        String nonExistentFile = "/path/to/nonexistent/file.docx";
        
        IOException exception = assertThrows(IOException.class, () -> {
            WordUtil.extractTextFromWord(nonExistentFile);
        });
        
        assertTrue(exception.getMessage().contains("Word文档不存在"));
    }

    @Test
    public void testExtractTextFromWordWithUnsupportedFormat() {
        // 创建一个临时的不支持格式的文件
        try {
            File tempFile = File.createTempFile("test", ".txt");
            tempFile.deleteOnExit();
            
            IOException exception = assertThrows(IOException.class, () -> {
                WordUtil.extractTextFromWord(tempFile.getAbsolutePath());
            });
            
            assertTrue(exception.getMessage().contains("不支持的文件格式"));
        } catch (IOException e) {
            fail("创建临时文件失败: " + e.getMessage());
        }
    }

    @Test
    public void testGetDocumentInfoWithNonExistentFile() {
        // 测试获取不存在文件的信息
        String nonExistentFile = "/path/to/nonexistent/file.docx";
        
        WordUtil.WordDocumentInfo info = WordUtil.getDocumentInfo(nonExistentFile);
        
        assertNull(info);
    }

    @Test
    public void testReadWordAndPrintToConsoleWithNonExistentFile() {
        // 测试读取不存在的文件（应该不抛出异常，而是在控制台输出错误信息）
        String nonExistentFile = "/path/to/nonexistent/file.docx";
        
        // 这个方法不应该抛出异常，而是在控制台输出错误信息
        assertDoesNotThrow(() -> {
            WordUtil.readWordAndPrintToConsole(nonExistentFile);
        });
    }

    @Test
    public void testReadWordAndPrintToConsoleWithMaxLines() {
        // 测试指定最大行数的读取方法
        String nonExistentFile = "/path/to/nonexistent/file.docx";
        int maxLines = 50;
        
        // 这个方法不应该抛出异常，而是在控制台输出错误信息
        assertDoesNotThrow(() -> {
            WordUtil.readWordAndPrintToConsole(nonExistentFile, maxLines);
        });
    }
}
