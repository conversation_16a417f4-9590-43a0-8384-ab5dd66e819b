# Contract Demo - PDF变量分析与Word文档处理工具

## 项目简介

这是一个基于Spring Boot的文档处理工具，能够读取PDF文件并提取其中的变量信息，同时支持Word文档的内容读取。项目整合了多个主流的开源文档处理库，提供强大的PDF文本提取、变量分析和Word文档读取功能。

## 功能特性

- 📄 **多PDF库支持**: 整合Apache PDFBox、iText7、OpenPDF等主流PDF处理库
- 🔍 **变量提取**: 自动识别PDF中的变量（格式：${1}、${2}、${3}...）
- 📝 **Word文档读取**: 支持.doc和.docx格式的Word文档内容读取
- ⚙️ **配置化**: 通过application.yml配置PDF和Word文件路径
- 🚀 **自动分析**: 应用启动时自动分析PDF变量和读取Word文档内容
- 🌐 **REST API**: 提供HTTP接口进行PDF变量分析和Word文档处理
- 📊 **详细报告**: 输出变量总数、每个变量的详细信息和Word文档内容

## 技术栈

- **框架**: Spring Boot 2.6.13
- **Java版本**: JDK 1.8
- **PDF处理库**:
  - Apache PDFBox 2.0.29
  - iText7 7.2.5
  - OpenPDF 1.3.30
- **Word处理库**:
  - Apache POI 5.2.4 (支持.doc和.docx格式)
- **工具库**: Hutool 5.8.38

## 项目结构

```
src/main/java/com/kang/contractdemo/
├── ContractDemoApplication.java     # 主启动类
├── config/
│   └── ContractConfig.java          # PDF和Word文档路径配置类
├── controller/
│   ├── PdfController.java           # PDF处理REST控制器
│   └── WordController.java          # Word文档处理REST控制器
├── service/
│   ├── PdfVariableService.java      # PDF变量分析服务
│   └── WordService.java             # Word文档处理服务
└── util/
    ├── PdfUtil.java                 # PDF工具类
    ├── WordUtil.java                # Word文档工具类
    └── WordReaderDemo.java          # Word文档读取演示类
```

## 配置说明

在 `src/main/resources/application.yml` 中配置PDF和Word文档文件路径：

```yaml
# 文档路径配置
contract:
  path: /Users/<USER>/Desktop/contract-template.pdf        # PDF文档路径
  word-path: /Users/<USER>/Desktop/contract-word.docx      # Word文档路径
```

## 使用方法

### 1. 启动应用

```bash
mvn spring-boot:run
```

应用启动后会自动分析配置的PDF文件和读取Word文档内容，并在控制台输出结果。

### 2. REST API接口

#### PDF相关接口

##### 分析PDF变量
```
GET /api/pdf/analyze
```

响应示例：
```json
{
  "success": true,
  "message": "PDF变量分析成功",
  "data": {
    "filePath": "/Users/<USER>/Desktop/contract-template.pdf",
    "variableCount": 5,
    "variables": ["${1}", "${2}", "${3}", "${4}", "${5}"]
  }
}
```

##### 获取PDF路径
```
GET /api/pdf/path
```

响应示例：
```json
{
  "success": true,
  "message": "获取PDF路径成功",
  "data": {
    "pdfPath": "/Users/<USER>/Desktop/contract-template.pdf"
  }
}
```

#### Word文档相关接口

##### 读取Word文档内容到控制台
```
GET /api/word/read
```

响应示例：
```json
{
  "success": true,
  "message": "Word文档读取成功，内容已输出到控制台",
  "data": null
}
```

##### 读取Word文档指定行数到控制台
```
GET /api/word/read/{maxLines}
```

响应示例：
```json
{
  "success": true,
  "message": "Word文档读取成功，前50行内容已输出到控制台",
  "data": {
    "maxLines": 50
  }
}
```

##### 获取Word文档路径
```
GET /api/word/path
```

响应示例：
```json
{
  "success": true,
  "message": "获取Word文档路径成功",
  "data": {
    "wordPath": "/Users/<USER>/Desktop/contract-word.docx"
  }
}
```

##### 获取Word文档信息
```
GET /api/word/info
```

响应示例：
```json
{
  "success": true,
  "message": "获取Word文档信息成功",
  "data": {
    "filePath": "/Users/<USER>/Desktop/contract-word.docx",
    "characterCount": 1500,
    "lineCount": 120,
    "fileSize": 25600
  }
}
```

##### 提取Word文档文本内容
```
GET /api/word/text
```

响应示例：
```json
{
  "success": true,
  "message": "提取Word文档文本成功",
  "data": {
    "text": "文档的完整文本内容...",
    "textLength": 1500
  }
}
```

##### 提取Word文档文本预览
```
GET /api/word/text/preview
```

响应示例：
```json
{
  "success": true,
  "message": "提取Word文档文本预览成功",
  "data": {
    "preview": "文档的前500字符内容...",
    "fullTextLength": 1500,
    "isPreview": true
  }
}
```

## 变量格式说明

本工具识别的变量格式为：`#{数字}`

示例：
- `#{1}` - 变量1
- `#{2}` - 变量2
- `#{10}` - 变量10
- `#{999}` - 变量999

## 控制台输出示例

### PDF变量分析输出示例

```
============================================================
		PDF变量分析结果
============================================================
PDF文件路径: /Users/<USER>/Desktop/contract-template.pdf
变量总数: 5 个

变量详情:
	1. #{1}
	2. #{2}
	3. #{3}
	4. #{4}
	5. #{5}

============================================================
分析完成！
============================================================
```

### Word文档读取输出示例

```
================================================================================
			Word文档内容读取
================================================================================
文件路径: /Users/<USER>/Desktop/contract-word.docx
文档总行数: 120
显示行数: 50
================================================================================
   1: 合同标题
   2:
   3: 第一条 合同条款
   4: 本合同由甲方和乙方共同签署...
   5:
   6: 第二条 权利义务
   7: 甲方应当履行以下义务...
   8: 乙方应当履行以下义务...
   ...
  50: 第十条 其他条款
================================================================================
注意: 文档还有 70 行未显示
Word文档读取完成！
================================================================================
```

## 依赖管理

项目使用Maven进行依赖管理，主要依赖包括：

```xml
<!-- Apache PDFBox -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
    <version>2.0.29</version>
</dependency>

<!-- iText7 -->
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>itext7-core</artifactId>
    <version>7.2.5</version>
    <type>pom</type>
</dependency>

<!-- OpenPDF -->
<dependency>
    <groupId>com.github.librepdf</groupId>
    <artifactId>openpdf</artifactId>
    <version>1.3.30</version>
</dependency>

<!-- Apache POI - Word文档处理 -->
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi</artifactId>
    <version>5.2.4</version>
</dependency>
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml</artifactId>
    <version>5.2.4</version>
</dependency>
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-scratchpad</artifactId>
    <version>5.2.4</version>
</dependency>
```

## 注意事项

1. 确保PDF和Word文档文件路径正确且文件存在
2. PDF文件需要是可读取的文本格式（非扫描件）
3. Word文档支持.doc和.docx格式
4. 变量格式必须严格按照 `${数字}` 的格式
5. 应用启动时会自动进行一次PDF分析和Word文档读取
6. Word文档路径配置为可选，如果未配置则跳过Word文档处理

## 使用示例

### 直接使用工具类

```java
// 读取Word文档并输出前1000行到控制台
WordUtil.readWordAndPrintToConsole("/path/to/document.docx");

// 读取Word文档并输出前50行到控制台
WordUtil.readWordAndPrintToConsole("/path/to/document.docx", 50);

// 提取Word文档文本内容
String content = WordUtil.extractTextFromWord("/path/to/document.docx");

// 获取Word文档信息
WordUtil.WordDocumentInfo info = WordUtil.getDocumentInfo("/path/to/document.docx");
```

## 扩展功能

- 支持多种PDF库，可根据需要选择最适合的库
- 支持.doc和.docx格式的Word文档读取
- 可扩展支持其他变量格式
- 可添加PDF生成和编辑功能
- 可集成更多文档处理特性
- 可扩展支持其他Office文档格式（Excel、PowerPoint等）

## 开发者

- 项目作者：Kang
- 技术支持：基于Spring Boot + 多文档处理库整合